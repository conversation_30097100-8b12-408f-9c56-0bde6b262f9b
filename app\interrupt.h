#ifndef INTERRUPT_H
#define INTERRUPT_H

#include "mydefine.h"

extern uint8_t task2_first_flag;

//主状态   
#define  STATE_IDLE   0
#define  TASK_1       1
#define  TASK_2       2


//任务2子状态
#define task2_state0 0
#define task2_state1 1
#define task2_state2 2

//任务1子状态
#define task1_state0 0
#define task1_state1 1
#define task1_state2 2


// 主状态结构体def
struct state_machine
{
   int MAIN_STATE;   	  // 空闲状态
   int STATE_TASK1;       // 第二问状态
   int STATE_TASK2;       // 第三问状态
};
extern uint32_t state_start_time; 
void State_Machine_init(void);
extern struct state_machine State_Machine;

#endif
