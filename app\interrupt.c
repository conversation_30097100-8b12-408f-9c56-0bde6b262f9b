#include "interrupt.h"

struct state_machine State_Machine;

void task2_proc(void);
void task1_proc(void);

void State_Machine_init()
{
    State_Machine.MAIN_STATE = STATE_IDLE;
    State_Machine.STATE_TASK1 = task1_state0;
    State_Machine.STATE_TASK2 = task2_state0;
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	if(htim -> Instance == TIM6)//20ms进入一次中断
	{
		Key_Proc();
		switch(State_Machine.MAIN_STATE)
		{
			case STATE_IDLE://IDLE
				
			break;
			case TASK_1://第二问逻辑
			{
				task1_proc();
			}
			break;
			case TASK_2://第二问逻辑
			{
				task2_proc();
			}
			break;
			default:
				
			break;
		}
		
	}
	
}
void task1_proc()
{
	switch(State_Machine.STATE_TASK1)
	{
	 	case task1_state0://自动追踪
			findred_control();
		break;
	}
}

void task2_proc()
{
	switch(State_Machine.STATE_TASK2)
	{
		case task2_state0:
			
		break;
	}
}
