#ifndef __PID_H
#define __PID_H

#include "mydefine.h"

typedef struct
{
	float output;
	float output_last;
	float target_val;//目标值
	float actual_val;//实际值
	float err;//当前偏差
	float err_last;//上次偏差
	float err_sum;//误差累计值
	float Kp,Ki,Kd;//比例，积分，微分系数
	
} tPid;

extern tPid motorA_pid;
extern tPid motorB_pid;
float PID_realize(tPid * pid,float actual_val);
void PID_init(void);
void findred_control(void);

#endif
