#ifndef SERVO_APP_H
#define SERVO_APP_H

#include "mydefine.h"

// 舵机1配置 - 水平轴(0-360度)
#define SERVO1_MIN_PULSE 500
#define SERVO1_MID_PULSE 1500
#define SERVO1_MAX_PULSE 2500
#define SERVO1_MIN_ANGLE 0
#define SERVO1_MAX_ANGLE 360

// 舵机2配置 - 垂直轴(10-170度)
#define SERVO2_MIN_PULSE 500
#define SERVO2_MID_PULSE 1500
#define SERVO2_MAX_PULSE 2500
#define SERVO2_MIN_ANGLE 10
#define SERVO2_MAX_ANGLE 170

// 舵机状态变量
extern float current_angle_x;
extern float current_angle_y;

// 舵机控制函数
void Servo_Init(void);
void Servo1_SetAngle(uint16_t angle);
void Servo2_SetAngle(uint16_t angle);

// 任务函数
void servo_task(void);

#endif
