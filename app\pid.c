#include "pid.h"
#include "math.h"

tPid motorA_pid;
tPid motorB_pid;

int16_t motorA_err;
int16_t motorB_err;

uint8_t first_flag = 0;
//给结构体类型变量赋初值
void PID_init()
{
    motorA_pid.actual_val=0.0;
    motorA_pid.target_val=0.00;
    motorA_pid.err=0.0;
    motorA_pid.err_last=0.0;
    motorA_pid.err_sum=0.0;
    motorA_pid.Kp=0.0043;//0.0015
    motorA_pid.Ki=0.0;
    motorA_pid.Kd=0.0;
    motorA_pid.output=0.0;
    motorA_pid.output_last = 0.0;
	
    motorB_pid.actual_val=0.0;
    motorB_pid.target_val=0.0;
    motorB_pid.err=0.0;
    motorB_pid.err_last=0.0;
    motorB_pid.err_sum=0.0;
    motorB_pid.Kp=0.0085;//0.0052
    motorB_pid.Ki=0.0;//0.002
    motorB_pid.Kd=0.0;//0.1
    motorB_pid.output=0.0;
	motorB_pid.output_last = 0.0;
}
 //PID控制函数
float PID_realize(tPid * pid,float err)
{
	pid->err = err;
	pid->err_sum += pid->err;

	if(first_flag == 0)
	{
		pid->Kd = 0;
		first_flag = 1;
	}
	else
	{
		motorB_pid.Kd = 0.025;
		motorA_pid.Kd = 0.025;
	}
	if(pid->err < 8.0f && pid->err > -8.0f)
	{
		pid->output = 0;
	}
	else
	{
		pid->output = pid->Kp * pid->err 
					+ pid->Ki * pid->err_sum 
					+ pid->Kd * (pid->err - pid->err_last);
	}

	if(pid->output > 80.0f) pid->output  = 80.0f;
	else if(pid->output < -80.0f) pid->output = -80.0f;

	pid->err_last = pid->err;

	return pid->output;
}

// 高级突变滤波算法 - 多重检测机制
float advanced_filter(float new_value, float *history, uint8_t *history_index, float *last_output)
{
    #define HISTORY_SIZE 5
    static uint8_t init_count = 0;

    // 初始化历史缓冲区
    if(init_count < HISTORY_SIZE) {
        history[*history_index] = new_value;
        *history_index = (*history_index + 1) % HISTORY_SIZE;
        *last_output = new_value;
        init_count++;
        return new_value;
    }

    // 计算历史平均值
    float avg = 0;
    for(uint8_t i = 0; i < HISTORY_SIZE; i++) {
        avg += history[i];
    }
    avg /= HISTORY_SIZE;

    // 计算标准差
    float variance = 0;
    for(uint8_t i = 0; i < HISTORY_SIZE; i++) {
        variance += (history[i] - avg) * (history[i] - avg);
    }
    float std_dev = sqrtf(variance / HISTORY_SIZE);

    // 动态阈值 - 基于历史数据的变化程度
    float dynamic_threshold = 20.0f + std_dev * 2.0f;

    // 突变检测
    if(fabsf(new_value - avg) > dynamic_threshold) {
        // 检测到突变，使用限幅处理
        float limited_value = *last_output + (new_value - *last_output) * 0.2f; // 限制变化率
        *last_output = limited_value;

        // 不更新历史缓冲区，保持稳定
        return limited_value;
    } else {
        // 正常值，更新历史缓冲区
        history[*history_index] = new_value;
        *history_index = (*history_index + 1) % HISTORY_SIZE;

        // 轻微平滑
        float output = *last_output * 0.4f + new_value * 0.6f;
        *last_output = output;
        return output;
    }
}

// 简单突变滤波算法
float filter_sudden_change(float new_value, float *last_value, float *last_filtered, float max_change_threshold)
{
    static uint8_t init_flag = 0;

    // 首次运行初始化
    if(init_flag == 0) {
        *last_value = new_value;
        *last_filtered = new_value;
        init_flag = 1;
        return new_value;
    }

    // 计算变化量
    float change = fabsf(new_value - *last_value);

    // 突变检测
    if(change > max_change_threshold) {
        // 检测到突变，使用限幅滤波
        float filtered_value;
        if(new_value > *last_value) {
            filtered_value = *last_value + max_change_threshold * 0.5f; // 允许一半的突变
        } else {
            filtered_value = *last_value - max_change_threshold * 0.5f;
        }

        // 一阶低通滤波平滑处理
        filtered_value = *last_filtered * 0.7f + filtered_value * 0.3f;

        *last_filtered = filtered_value;
        *last_value = new_value; // 更新历史值用于下次比较

        return filtered_value;
    } else {
        // 正常变化，轻微滤波
        float filtered_value = *last_filtered * 0.3f + new_value * 0.7f;
        *last_filtered = filtered_value;
        *last_value = new_value;
        return filtered_value;
    }
}

void findred_control(void)
{
    // 滤波参数配置
    #define FILTER_ENABLE 1          // 1=启用滤波, 0=禁用滤波
    #define MAX_CHANGE_X 75.0f       // X轴最大允许变化量
    #define MAX_CHANGE_Y 50.0f       // Y轴最大允许变化量
    #define FILTER_STRENGTH 0.9f     // 滤波强度 (0.1-0.9, 越大越平滑)

    static float last_x_error = 0, last_y_error = 0;
    static float last_filtered_x = 0, last_filtered_y = 0;

    float filtered_x_error, filtered_y_error;

    #if FILTER_ENABLE
        // 突变滤波处理
        filtered_x_error = filter_sudden_change((float)x_axis_error, &last_x_error, &last_filtered_x, MAX_CHANGE_X);
        filtered_y_error = filter_sudden_change((float)y_axis_error, &last_y_error, &last_filtered_y, MAX_CHANGE_Y);
    #else
        // 不使用滤波，直接使用原始误差
        filtered_x_error = (float)x_axis_error;
        filtered_y_error = (float)y_axis_error;
    #endif

    // 使用滤波后的误差进行PID控制
    current_angle_x += PID_realize(&motorA_pid, filtered_x_error);
    current_angle_y += PID_realize(&motorB_pid, filtered_y_error);

    // 调试输出 - 可以看到滤波效果
    static uint32_t debug_count = 0;
    if(++debug_count % 15 == 0) { // 每15次输出一次，减少刷屏
        my_printf(&huart1,"Raw:X=%d,Y=%d Filt:X=%.1f,Y=%.1f\r\n",
                 x_axis_error, y_axis_error, filtered_x_error, filtered_y_error);
    }

    Servo1_SetAngle((int16_t)current_angle_y);
    Servo2_SetAngle((int16_t)current_angle_x);
}
	
	
