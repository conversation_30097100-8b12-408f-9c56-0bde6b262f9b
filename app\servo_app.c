#include "servo_app.h"

// 舵机状态变量
float current_angle_x = 90;
float current_angle_y = 180;

void Servo_Init(void)
{
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_1);
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_2);
    Servo1_SetAngle(180);//Y
    Servo2_SetAngle(90);//X
    HAL_Delay(1000);
}

void Servo1_SetAngle(uint16_t angle)
{
    uint16_t pulse_width;
    if(angle > SERVO1_MAX_ANGLE) angle = SERVO1_MAX_ANGLE;
    pulse_width = SERVO1_MAX_PULSE - (angle * (SERVO1_MAX_PULSE - SERVO1_MIN_PULSE)) / 360;
    __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_1, pulse_width);
}

void Servo2_SetAngle(uint16_t angle)
{
    uint16_t pulse_width;
    if(angle < SERVO2_MIN_ANGLE) angle = SERVO2_MIN_ANGLE;
    if(angle > SERVO2_MAX_ANGLE) angle = SERVO2_MAX_ANGLE;
    pulse_width = SERVO2_MIN_PULSE + (angle * (SERVO2_MAX_PULSE - SERVO2_MIN_PULSE)) / 180;
    __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_2, pulse_width);
}

void servo_task(void)
{
  Servo2_SetAngle(95);
  HAL_Delay(1500);
  Servo1_SetAngle(200);
  HAL_Delay(1500);
  Servo2_SetAngle(85);
  HAL_Delay(1500);
  Servo1_SetAngle(160);
  HAL_Delay(1500);
}
