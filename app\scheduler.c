#include "scheduler.h"
#include "servo_app.h"      // 舵机应用
#include "usart_app.h"      // 串口应用

uint8_t Key_Val,Key_Down,Key_Up,Key_Old;
uint8_t flag;

uint8_t Key_Read()
{
	uint8_t temp = 0;
	
	if(HAL_GPIO_ReadPin(KEY0_GPIO_Port,KEY0_Pin) == RESET) temp = 1;
	if(HAL_GPIO_ReadPin(KEY1_GPIO_Port,KEY1_Pin) == RESET) temp = 2;
	
	return temp;
}

void Key_Proc()
{
	Key_Val = Key_Read();
	Key_Down = Key_Val & ( Key_Val ^ Key_Old);
	Key_Up = ~Key_Val & ( Key_Val ^ Key_Old);
	Key_Old = Key_Val;
	
	if(Key_Down == 1) 
	{
		State_Machine.MAIN_STATE = TASK_1;
		State_Machine.STATE_TASK1 = task1_state0;
	}
	else if(Key_Down == 2)
	{
		State_Machine.MAIN_STATE = TASK_2;
	    State_Machine.STATE_TASK2 = task2_state0;
	}
}
uint8_t task_num;

typedef struct
{
	void(*task_func)(void);
	uint32_t rate_ms;
	uint32_t last_run;
}task_t;


// 任务列表
static task_t scheduler_task[] =
{
	{uart_task,10,0},
};

void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}
void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = HAL_GetTick();
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}
