#include "usart_app.h"
#include "servo_app.h"  // 包含视觉追踪函数

uint32_t uart_rx_ticks;
uint8_t uart_rx_index;
uint8_t uart_rx_buffer[128] = {0};
uint8_t uart_flag;
float x_axis_error = 0; // x轴误差值
float y_axis_error = 0; // y轴误差值

/**
 * @brief 串口应用初始化
 * @param None
 * @retval None
 */

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512]; 
	va_list arg;      
	int len;          

	va_start(arg, format);

	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if (huart->Instance == UART4)
	{
		uart_rx_ticks = uwTick;

		// 接收确认信号 (可选调试)
		// my_printf(&huart1, "RX_BYTE: %c (0x%02X)\n", uart_rx_buffer[uart_rx_index], uart_rx_buffer[uart_rx_index]);

		// 防止缓冲区溢出
		if(uart_rx_index < (sizeof(uart_rx_buffer) - 1)) {
			uart_rx_index++;
		}

		// 启动下一次接收
		HAL_UART_Receive_IT(&huart4, &uart_rx_buffer[uart_rx_index], 1);
	}
}


void uart4_process()
{
    // 协议格式: 0x22 + 符号位1 + 数据位1 + 符号位2 + 数据位2 + 0x33
    if(uart_rx_index < 6) return; // 数据包长度检查(6字节)

    // 查找完整数据包
    for(uint8_t i = 0; i <= uart_rx_index - 6; i++) {
        // 检查帧头
        if(uart_rx_buffer[i] == 0x22) {
            // 检查帧尾
            if(uart_rx_buffer[i + 5] == 0x33) {
                uint8_t sign1 = uart_rx_buffer[i + 1]; // 第一个符号位
                uint8_t data1 = uart_rx_buffer[i + 2]; // 第一个数据位
                uint8_t sign2 = uart_rx_buffer[i + 3]; // 第二个符号位
                uint8_t data2 = uart_rx_buffer[i + 4]; // 第二个数据位

                // 验证符号位有效性
                if((sign1 != 0x00 && sign1 != 0x01) || (sign2 != 0x00 && sign2 != 0x01)) {
                    continue; // 无效符号位，继续查找
                }

                // 计算并存储x轴和y轴误差值
                x_axis_error = (sign1 == 0x00) ? (int16_t)data1 : -(int16_t)data1;
                y_axis_error = (sign2 == 0x00) ? (int16_t)data2 : -(int16_t)data2;

                // 调试输出
//                my_printf(&huart1, "RX: X=%s%d Y=%s%d -> X_err:%d Y_err:%d\n",
//                         (sign1 == 0x00) ? "+" : "-", data1,
//                         (sign2 == 0x00) ? "+" : "-", data2,
//                         x_axis_error, y_axis_error);

                return; // 处理完成，退出
            }
        }
    }
}

void uart_task(void)
{
		if (uart_rx_index == 0)	return;
		if (uwTick - uart_rx_ticks > 10)  // 减少等待时间，提高响应速度
		{
		// 解析串口数据
		uart4_process();

		// 清空接收缓冲区
		memset(uart_rx_buffer, 0, uart_rx_index);
		uart_rx_index = 0;

		// 重新设置接收指针到缓冲区开始
		huart4.pRxBuffPtr = uart_rx_buffer;
		}
}

