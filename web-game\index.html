<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字猜谜游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .title {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .info {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .input-area {
            margin-bottom: 25px;
        }
        
        .guess-input {
            width: 200px;
            padding: 15px;
            font-size: 1.2em;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin-right: 10px;
            text-align: center;
            transition: border-color 0.3s;
        }
        
        .guess-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }
        
        .submit-btn, .restart-btn {
            padding: 15px 25px;
            font-size: 1.1em;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .restart-btn {
            background: #f39c12;
            color: white;
            margin-top: 20px;
        }
        
        .restart-btn:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }
        
        .feedback {
            margin: 25px 0;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .feedback-text {
            font-size: 1.3em;
            font-weight: bold;
            padding: 10px 20px;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .feedback-text.success {
            background: #2ecc71;
            color: white;
        }
        
        .feedback-text.hint {
            background: #3498db;
            color: white;
        }
        
        .stats {
            color: #555;
            font-size: 1.2em;
            margin: 20px 0;
        }
        
        .count {
            color: #e74c3c;
            font-weight: bold;
            font-size: 1.3em;
        }
        
        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 2em;
            }
            
            .guess-input {
                width: 150px;
                margin-right: 5px;
                margin-bottom: 10px;
            }
            
            .input-area {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }
            
            .submit-btn {
                width: 150px;
            }
        }
    </style>
</head>
<body>
    <div id="game-container" class="container">
        <h1 id="game-title" class="title">数字猜谜游戏</h1>
        
        <div id="game-info" class="info">
            <p>我想了一个1到100之间的数字，你能猜出来吗？</p>
        </div>
        
        <div id="input-section" class="input-area">
            <input type="number" id="guess-input" class="guess-input" 
                   min="1" max="100" placeholder="请输入1-100的数字">
            <button id="submit-btn" class="submit-btn">提交猜测</button>
        </div>
        
        <div id="feedback-section" class="feedback">
            <p id="feedback-text" class="feedback-text"></p>
        </div>
        
        <div id="stats-section" class="stats">
            <p>猜测次数: <span id="guess-count" class="count">0</span></p>
        </div>
        
        <div id="control-section" class="controls">
            <button id="restart-btn" class="restart-btn">重新开始</button>
        </div>
    </div>

    <script>
        // 游戏状态变量
        let targetNumber = 0;
        let guessCount = 0;
        let gameActive = true;
        
        // DOM元素引用
        const guessInput = document.getElementById('guess-input');
        const submitBtn = document.getElementById('submit-btn');
        const restartBtn = document.getElementById('restart-btn');
        const feedbackText = document.getElementById('feedback-text');
        const guessCountDisplay = document.getElementById('guess-count');
        
        // 初始化游戏
        function initGame() {
            targetNumber = Math.floor(Math.random() * 100) + 1; // 生成1-100随机数
            guessCount = 0;
            gameActive = true;
            updateGuessCount();
            feedbackText.textContent = '';
            feedbackText.className = 'feedback-text';
            guessInput.value = '';
            guessInput.disabled = false;
            submitBtn.disabled = false;
            guessInput.focus();
        }
        
        // 更新猜测次数显示
        function updateGuessCount() {
            guessCountDisplay.textContent = guessCount;
        }
        
        // 输入验证
        function validateInput(input) {
            if (input === '') {
                return { valid: false, message: '请输入一个数字！' };
            }
            const num = parseInt(input);
            if (isNaN(num)) {
                return { valid: false, message: '请输入有效的数字！' };
            }
            if (num < 1 || num > 100) {
                return { valid: false, message: '请输入1到100之间的数字！' };
            }
            return { valid: true, number: num };
        }
        
        // 处理猜测
        function makeGuess() {
            if (!gameActive) return;
            
            const inputValue = guessInput.value.trim();
            const validation = validateInput(inputValue);
            
            if (!validation.valid) {
                showFeedback(validation.message, 'hint');
                return;
            }
            
            const userGuess = validation.number;
            guessCount++;
            updateGuessCount();
            
            // 比较逻辑
            if (userGuess === targetNumber) {
                // 猜对了
                showFeedback(`🎉 恭喜你！答案就是 ${targetNumber}！你用了 ${guessCount} 次猜对了！`, 'success');
                gameActive = false;
                guessInput.disabled = true;
                submitBtn.disabled = true;
            } else if (userGuess > targetNumber) {
                // 太大了
                showFeedback('太大了！试试更小的数字', 'hint');
            } else {
                // 太小了
                showFeedback('太小了！试试更大的数字', 'hint');
            }
            
            // 清空输入框并聚焦
            guessInput.value = '';
            if (gameActive) {
                guessInput.focus();
            }
        }
        
        // 显示反馈信息
        function showFeedback(message, type) {
            feedbackText.textContent = message;
            feedbackText.className = `feedback-text ${type}`;
        }
        
        // 重新开始游戏
        function restartGame() {
            initGame();
            showFeedback('新游戏开始！我已经想好了一个新数字', 'hint');
        }
        
        // 事件监听器
        submitBtn.addEventListener('click', makeGuess);
        restartBtn.addEventListener('click', restartGame);
        
        // 回车键支持
        guessInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault(); // 防止表单默认提交
                makeGuess();
            }
        });
        
        // 页面加载完成后初始化游戏
        document.addEventListener('DOMContentLoaded', function() {
            initGame();
            showFeedback('游戏开始！我已经想好了一个1到100之间的数字', 'hint');
        });
    </script>
</body>
</html>